/**
 * 优化的 Agent 列表 API 路由
 * 提供可用 agents 的列表信息
 * 支持快速响应模式和智能缓存
 */

import { NextResponse } from 'next/server';
import { getAllIntegrations } from '@workspace/agent-registry/server';

// Agent ID 到路由路径的映射规则
const getAgentRoute = (agentId: string): string => {
  // 使用新的参数化路由：直接使用 agent ID
  return `/${agentId}`;
};

// 预定义的基础agents信息，用于快速响应
const BASE_AGENTS = [
  {
    id: 'haiku',
    name: 'Haiku Agent',
    description: '专门用于创作诗歌的AI助手，能够创作优美的中英双语诗歌',
    route: '/haiku',
    type: 'integration',
    originalId: 'haiku',
    version: '0.0.1',
    isBaseline: true,
    status: 'ready',
    capabilities: ['poetry', 'creative-writing', 'multilingual']
  },
  {
    id: 'mario',
    name: 'Mario Agent',
    description: 'Mario是基于TestNG的Java自动化测试框架，可以自动生成EC和Thrift泛化的自动化测试用例',
    route: '/mario',
    type: 'integration',
    originalId: 'mario',
    version: '0.0.1',
    isBaseline: true,
    status: 'ready',
    capabilities: ['testing', 'automation', 'java', 'code-generation']
  }
];

// 缓存机制
let cachedAgents: any[] | null = null;
let cacheTimestamp = 0;
const CACHE_TTL = 3 * 60 * 1000; // 3分钟缓存

// 性能监控
let requestCount = 0;
let fastModeCount = 0;

/**
 * 获取可用的 agents 列表
 * 支持查询参数：
 * - fast=true: 快速模式，立即返回基础agents
 * - full=true: 完整模式，从注册中心获取最新数据
 */
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const fastMode = searchParams.get('fast') === 'true';

    // 快速模式：立即返回基础agents，不查询注册中心
    if (fastMode) {
      console.log('Fast mode: returning baseline agents immediately');
      return NextResponse.json({
        agents: BASE_AGENTS,
        mode: 'fast',
        timestamp: new Date().toISOString()
      });
    }

    // 完整模式：从integration注册中心获取所有integrations
    console.log('Full mode: querying integration registry');
    const allIntegrations = await getAllIntegrations();

    // 合并注册中心数据和基础数据
    const registryAgents = allIntegrations.map((integration) => ({
      id: integration.id,
      name: integration.name,
      description: integration.description,
      route: getAgentRoute(integration.id),
      type: 'integration',
      originalId: integration.id,
      version: integration.version,
      isBaseline: false
    }));

    // 确保基础agents始终存在，如果注册中心没有对应数据则使用基础版本
    const mergedAgents = [...registryAgents];
    BASE_AGENTS.forEach((baseAgent) => {
      if (!registryAgents.find((agent) => agent.id === baseAgent.id)) {
        mergedAgents.push(baseAgent);
      }
    });

    return NextResponse.json({
      agents: mergedAgents,
      mode: 'full',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting agents list:', error);

    // 错误时返回基础agents
    return NextResponse.json({
      agents: BASE_AGENTS,
      mode: 'fallback',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
