/**
 * Agent 列表 API 路由
 * 提供可用 agents 的列表信息
 */

import { NextResponse } from 'next/server';
import { getAllIntegrations } from '@workspace/agent-registry/server';

// Agent ID 到路由路径的映射规则
const getAgentRoute = (agentId: string): string => {
  return `/${agentId}`;
};

/**
 * 获取可用的 agents 列表
 */
export async function GET() {
  try {
    console.log('Getting agents list from registry...');
    const allIntegrations = await getAllIntegrations();

    const agents = allIntegrations.map((integration) => ({
      id: integration.id,
      name: integration.name,
      description: integration.description,
      route: getAgentRoute(integration.id),
      type: 'integration',
      originalId: integration.id,
      version: integration.version
    }));

    return NextResponse.json({
      agents,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting agents list:', error);
    return NextResponse.json({
      agents: [],
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
