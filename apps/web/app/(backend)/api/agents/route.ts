/**
 * 优化的 Agent 列表 API 路由
 * 提供可用 agents 的列表信息
 * 支持快速响应模式和智能缓存
 */

import { NextResponse } from 'next/server';
import { getAllIntegrations } from '@workspace/agent-registry/server';

// Agent ID 到路由路径的映射规则
const getAgentRoute = (agentId: string): string => {
  // 使用新的参数化路由：直接使用 agent ID
  return `/${agentId}`;
};

// 预定义的基础agents信息，用于快速响应
const BASE_AGENTS = [
  {
    id: 'haiku',
    name: 'Haiku Agent',
    description: '专门用于创作诗歌的AI助手，能够创作优美的中英双语诗歌',
    route: '/haiku',
    type: 'integration',
    originalId: 'haiku',
    version: '0.0.1',
    isBaseline: true,
    status: 'ready',
    capabilities: ['poetry', 'creative-writing', 'multilingual']
  },
  {
    id: 'mario',
    name: 'Mario Agent',
    description: 'Mario是基于TestNG的Java自动化测试框架，可以自动生成EC和Thrift泛化的自动化测试用例',
    route: '/mario',
    type: 'integration',
    originalId: 'mario',
    version: '0.0.1',
    isBaseline: true,
    status: 'ready',
    capabilities: ['testing', 'automation', 'java', 'code-generation']
  }
];

// 缓存机制
let cachedAgents: any[] | null = null;
let cacheTimestamp = 0;
const CACHE_TTL = 3 * 60 * 1000; // 3分钟缓存

// 性能监控
let requestCount = 0;
let fastModeCount = 0;

/**
 * 获取缓存的agents列表
 */
async function getCachedAgents(): Promise<any[]> {
  const now = Date.now();

  // 如果缓存有效，直接返回
  if (cachedAgents && (now - cacheTimestamp) < CACHE_TTL) {
    return cachedAgents;
  }

  try {
    // 从integration注册中心获取所有integrations
    console.log('Refreshing agents cache from registry...');
    const allIntegrations = await getAllIntegrations();

    // 合并注册中心数据和基础数据
    const registryAgents = allIntegrations.map((integration) => ({
      id: integration.id,
      name: integration.name,
      description: integration.description,
      route: getAgentRoute(integration.id),
      type: 'integration',
      originalId: integration.id,
      version: integration.version,
      isBaseline: false,
      status: 'ready',
      capabilities: getAgentCapabilities(integration.id)
    }));

    // 确保基础agents始终存在，如果注册中心没有对应数据则使用基础版本
    const mergedAgents = [...registryAgents];
    BASE_AGENTS.forEach((baseAgent) => {
      if (!registryAgents.find((agent) => agent.id === baseAgent.id)) {
        mergedAgents.push(baseAgent);
      }
    });

    // 更新缓存
    cachedAgents = mergedAgents;
    cacheTimestamp = now;

    console.log(`Cached ${mergedAgents.length} agents`);
    return mergedAgents;
  } catch (error) {
    console.error('Error refreshing agents cache:', error);

    // 如果有旧缓存，返回旧缓存
    if (cachedAgents) {
      console.log('Using stale cache due to error');
      return cachedAgents;
    }

    // 否则返回基础agents
    console.log('Using baseline agents due to error');
    cachedAgents = BASE_AGENTS;
    cacheTimestamp = now;
    return BASE_AGENTS;
  }
}

/**
 * 优化的 agents 列表获取函数
 * 支持查询参数：
 * - fast=true: 快速模式，优先返回缓存或基础agents
 * - full=true: 完整模式，强制刷新缓存
 * - status=true: 包含性能统计信息
 */
export async function GET(request: Request) {
  const startTime = Date.now();
  requestCount++;

  try {
    const { searchParams } = new URL(request.url);
    const fastMode = searchParams.get('fast') === 'true';
    const fullMode = searchParams.get('full') === 'true';
    const includeStatus = searchParams.get('status') === 'true';

    let agents: any[];
    let mode: string;

    if (fastMode) {
      // 快速模式：优先返回缓存，如果没有缓存则返回基础agents
      fastModeCount++;

      if (cachedAgents && (Date.now() - cacheTimestamp) < CACHE_TTL) {
        agents = cachedAgents;
        mode = 'fast-cached';
        console.log('Fast mode: returning cached agents');
      } else {
        agents = BASE_AGENTS;
        mode = 'fast-baseline';
        console.log('Fast mode: returning baseline agents');
      }
    } else if (fullMode) {
      // 完整模式：强制刷新缓存
      console.log('Full mode: forcing cache refresh');
      cachedAgents = null; // 清除缓存
      agents = await getCachedAgents();
      mode = 'full-refresh';
    } else {
      // 默认模式：使用缓存机制
      agents = await getCachedAgents();
      mode = cachedAgents === agents ? 'cached' : 'refreshed';
    }

    const responseTime = Date.now() - startTime;

    const response: any = {
      agents,
      mode,
      timestamp: new Date().toISOString(),
      responseTime: `${responseTime}ms`
    };

    // 包含性能统计信息
    if (includeStatus) {
      response.stats = {
        totalRequests: requestCount,
        fastModeRequests: fastModeCount,
        cacheAge: cachedAgents ? Date.now() - cacheTimestamp : 0,
        cacheValid: cachedAgents && (Date.now() - cacheTimestamp) < CACHE_TTL
      };
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error getting agents list:', error);
    const responseTime = Date.now() - startTime;

    // 错误时返回基础agents
    return NextResponse.json({
      agents: BASE_AGENTS,
      mode: 'fallback',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
      responseTime: `${responseTime}ms`
    });
  }
}
