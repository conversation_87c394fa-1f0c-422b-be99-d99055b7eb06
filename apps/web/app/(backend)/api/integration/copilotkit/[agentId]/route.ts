import { CopilotRuntime, copilotRuntimeNextJSAppRouterEndpoint, OpenAIAdapter } from '@copilotkit/runtime';
import { NextRequest } from 'next/server';
import OpenAI from 'openai';
import { MastraAgent } from '@ag-ui/mastra';
import { Mastra } from '@mastra/core';
import {
  getIntegration,
  hasIntegration,
  createAgent,
  getIntegrationList
} from '@workspace/agent-registry/server';

/**
 * 创建OpenAI实例和服务适配器
 */
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || '1914304559263223873',
  baseURL: process.env.OPENAI_BASE_URL || 'https://aigc.sankuai.com/v1/openai/native/'
});

/**
 * 创建CopilotKit的OpenAI适配器
 */
const serviceAdapter = new OpenAIAdapter({
  openai,
  model: 'gpt-4o-2024-11-20'
});



export const POST = async (request: NextRequest) => {
  try {
    console.log('=== CopilotKit API Route Called ===');
    console.log('Request URL:', request.url);
    console.log('Request method:', request.method);

    // 从 URL 路径中提取 agentId，去除查询参数
    const urlParts = request.url.split('/');
    const lastPart = urlParts.pop() || '';
    const agentId = lastPart.split('?')[0]; // 去除查询参数
    console.log('🔍 Extracted agentId:', agentId);

    if (!agentId) {
      console.error('❌ Agent ID not provided in URL');
      return new Response(
        JSON.stringify({
          error: 'Agent ID not provided',
          details: 'Agent ID is required in the URL path'
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    console.log('✅ Using agent ID:', agentId);

    // 验证 agent 配置是否存在
    console.log('🔍 Checking if agent configuration exists...');
    const agentInfo = await getIntegration(agentId);
    if (!agentInfo) {
      console.error(`❌ Agent configuration for ${agentId} not found`);
      const availableAgents = await getIntegrationList();
      console.log(
        'Available agents:',
        availableAgents.map((a) => a.id)
      );
      return new Response(
        JSON.stringify({
          error: 'Agent configuration not found',
          details: `Agent ${agentId} is not configured`,
          requestedAgent: agentId,
          availableAgents: availableAgents.map((a) => a.id)
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    console.log('✅ Agent configuration found:', agentInfo.name);

    // 检查 agent 是否存在于注册中心
    console.log('🔍 Checking if agent exists in registry...');
    if (!(await hasIntegration(agentId))) {
      console.error(`❌ Agent ${agentId} not found in registry`);
      return new Response(
        JSON.stringify({
          error: 'Agent not found in registry',
          details: `Agent ${agentId} is not registered in the agent registry`,
          requestedAgent: agentId,
          availableAgents: (await getIntegrationList()).map((integration) => integration.id)
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    console.log('✅ Agent exists in registry');

    // 从注册中心获取指定的 Mastra agent 实例
    console.log('🚀 Creating Mastra agent instance...');
    const mastraAgent = await createAgent(agentId);
    console.log('✅ Retrieved Mastra agent instance');
    console.log('📝 Agent basic info:', {
      name: mastraAgent.name,
      hasInstructions: !!mastraAgent.instructions,
      instructionsLength: mastraAgent.instructions?.length || 0,
      hasTools: !!mastraAgent.tools,
      toolsCount: mastraAgent.tools ? Object.keys(mastraAgent.tools).length : 0,
      hasModel: !!mastraAgent.model,
      agentType: typeof mastraAgent,
      agentConstructor: mastraAgent.constructor?.name
    });

    // 详细检查agent属性
    console.log('🔍 Detailed agent inspection:');
    console.log('- Agent name:', mastraAgent.name);
    console.log('- Agent instructions preview:', mastraAgent.instructions?.substring(0, 200) + '...');
    console.log('- Agent tools:', mastraAgent.tools ? Object.keys(mastraAgent.tools) : 'No tools');
    console.log('- Agent model info:', mastraAgent.model ? 'Model present' : 'No model');

    // 创建 Mastra 实例包含我们的 agent
    console.log('🏗️ Creating Mastra instance...');
    const mastra = new Mastra({
      agents: { [agentId]: mastraAgent }
    });

    console.log('✅ Mastra instance created');
    console.log('📋 Mastra agents info:', {
      agentKeys: Object.keys((mastra as any).agents || {}),
      agentCount: Object.keys((mastra as any).agents || {}).length,
      hasTargetAgent: !!((mastra as any).agents && (mastra as any).agents[agentId])
    });

    // 创建运行时上下文
    console.log('🔧 Creating runtime context...');
    const runtimeContext = new Map();
    runtimeContext.set('user-id', 'default-user');
    runtimeContext.set('agent-id', agentId);
    runtimeContext.set('registry', {}); // 添加required registry属性
    console.log('✅ Runtime context created with keys:', Array.from(runtimeContext.keys()));

    // 将 Mastra agent 转换为 CopilotKit 兼容格式
    console.log('🔄 Converting Mastra agent to CopilotKit format...');
    console.log('🔧 MastraAgent.getLocalAgents input parameters:', {
      resourceId: agentId,
      mastraAgentsCount: Object.keys((mastra as any).agents || {}).length,
      runtimeContextKeys: Array.from(runtimeContext.keys())
    });

    let copilotAgents;
    try {
      copilotAgents = MastraAgent.getLocalAgents({
        resourceId: agentId,
        mastra,
        runtimeContext: runtimeContext as any
      });
      console.log('✅ MastraAgent.getLocalAgents completed successfully');
    } catch (conversionError) {
      console.error('❌ Error in MastraAgent.getLocalAgents:', conversionError);
      console.error('Conversion error details:', {
        message: conversionError instanceof Error ? conversionError.message : 'Unknown error',
        stack: conversionError instanceof Error ? conversionError.stack : 'No stack trace'
      });
      throw conversionError;
    }

    console.log('📊 Converted agents info:', {
      agentsCount: Object.keys(copilotAgents || {}).length,
      agentsArray: Array.isArray(copilotAgents),
      agentsType: typeof copilotAgents,
      agentKeys: Object.keys(copilotAgents || {})
    });

    const agentValues = Object.values(copilotAgents || {});
    if (agentValues.length > 0) {
      console.log('📝 First agent details:', {
        name: agentValues[0]?.agentId,
        agentType: agentValues[0]?.constructor?.name,
        agentKeys: Object.keys(agentValues[0] || {})
      });
    } else {
      console.warn('⚠️ No agents returned from conversion or invalid format');
    }

    // 创建 CopilotRuntime，添加 agents
    console.log('🏗️ Creating CopilotRuntime with agents...');
    console.log('🔧 CopilotRuntime input:', {
      agentsProvided: !!copilotAgents,
      agentsCount: Object.keys(copilotAgents || {}).length,
      agentsIsRecord: typeof copilotAgents === 'object' && !Array.isArray(copilotAgents)
    });

    let runtime;
    try {
      runtime = new CopilotRuntime({
        agents: copilotAgents
      });
      console.log('✅ CopilotRuntime created successfully');
    } catch (runtimeError) {
      console.error('❌ Error creating CopilotRuntime:', runtimeError);
      console.error('Runtime error details:', {
        message: runtimeError instanceof Error ? runtimeError.message : 'Unknown error',
        stack: runtimeError instanceof Error ? runtimeError.stack : 'No stack trace'
      });
      throw runtimeError;
    }

    console.log('🎯 CopilotRuntime created with', Object.keys(copilotAgents || {}).length, 'agents for:', agentId);

    // 创建endpoint handler
    console.log('🔧 Creating copilotRuntimeNextJSAppRouterEndpoint...');
    let handleRequest;
    try {
      const endpointResult = copilotRuntimeNextJSAppRouterEndpoint({
        runtime,
        serviceAdapter,
        endpoint: `/api/integration/copilotkit/${agentId}`
      });
      handleRequest = endpointResult.handleRequest;
      console.log('✅ Endpoint handler created successfully');
    } catch (endpointError) {
      console.error('❌ Error creating endpoint handler:', endpointError);
      console.error('Endpoint error details:', {
        message: endpointError instanceof Error ? endpointError.message : 'Unknown error',
        stack: endpointError instanceof Error ? endpointError.stack : 'No stack trace'
      });
      throw endpointError;
    }

    console.log('🎯 About to handle request for agent:', agentId);
    const response = await handleRequest(request);
    console.log('✅ Request handled successfully, response status:', response.status);

    return response;
  } catch (error) {
    console.error('💥 CopilotKit API error:', error);
    console.error('🔍 Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      name: error instanceof Error ? error.name : 'Unknown error type',
      cause: error instanceof Error ? error.cause : undefined
    });

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        details: errorMessage,
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
        agentId: request.url.split('/').pop()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
};
