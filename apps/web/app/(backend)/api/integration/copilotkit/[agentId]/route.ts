import { CopilotRuntime, copilotRuntimeNextJSAppRouterEndpoint, OpenAIAdapter } from '@copilotkit/runtime';
import { NextRequest } from 'next/server';
import OpenAI from 'openai';
import { MastraAgent } from '@ag-ui/mastra';
import { Mastra } from '@mastra/core';
import {
  getIntegration,
  hasIntegration,
  getAllIntegrations,
  createAgent,
  getIntegrationList
} from '@workspace/agent-registry/server';

/**
 * 创建OpenAI实例和服务适配器
 */
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || '1914304559263223873',
  baseURL: process.env.OPENAI_BASE_URL || 'https://aigc.sankuai.com/v1/openai/native/'
});

/**
 * 创建CopilotKit的OpenAI适配器
 */
const serviceAdapter = new OpenAIAdapter({
  openai,
  model: 'gpt-4o-2024-11-20'
});

// 预定义的已知agents，用于快速验证
const KNOWN_AGENTS = new Set(['haiku', 'mario']);

// 缓存的agent验证结果，避免重复验证
const agentValidationCache = new Map<string, { isValid: boolean; timestamp: number; agentInfo?: any }>();
const VALIDATION_CACHE_TTL = 2 * 60 * 1000; // 2分钟缓存

/**
 * 快速验证agent是否存在（带缓存）
 */
async function validateAgent(agentId: string): Promise<{ isValid: boolean; agentInfo?: any }> {
  const now = Date.now();
  const cached = agentValidationCache.get(agentId);

  // 如果缓存有效，直接返回
  if (cached && (now - cached.timestamp) < VALIDATION_CACHE_TTL) {
    return { isValid: cached.isValid, agentInfo: cached.agentInfo };
  }

  // 对于已知agents，快速验证
  if (KNOWN_AGENTS.has(agentId)) {
    try {
      const agentInfo = await getIntegration(agentId);
      if (agentInfo) {
        agentValidationCache.set(agentId, { isValid: true, timestamp: now, agentInfo });
        return { isValid: true, agentInfo };
      }
    } catch (error) {
      console.warn(`Failed to get integration for known agent ${agentId}:`, error);
    }

    // 即使获取详细信息失败，已知agents仍然被认为是有效的
    const fallbackInfo = { id: agentId, name: `${agentId.charAt(0).toUpperCase() + agentId.slice(1)} Agent` };
    agentValidationCache.set(agentId, { isValid: true, timestamp: now, agentInfo: fallbackInfo });
    return { isValid: true, agentInfo: fallbackInfo };
  }

  // 对于未知agents，进行完整验证
  try {
    const [agentInfo, hasAgent] = await Promise.all([
      getIntegration(agentId),
      hasIntegration(agentId)
    ]);

    const isValid = !!(agentInfo && hasAgent);
    agentValidationCache.set(agentId, { isValid, timestamp: now, agentInfo });
    return { isValid, agentInfo };
  } catch (error) {
    console.error(`Error validating agent ${agentId}:`, error);
    agentValidationCache.set(agentId, { isValid: false, timestamp: now });
    return { isValid: false };
  }
}

export const POST = async (request: NextRequest) => {
  try {
    console.log('=== CopilotKit API Route Called ===');
    console.log('Request URL:', request.url);
    console.log('Request method:', request.method);

    // 从 URL 路径中提取 agentId，去除查询参数
    const urlParts = request.url.split('/');
    const lastPart = urlParts.pop() || '';
    const agentId = lastPart.split('?')[0]; // 去除查询参数
    console.log('🔍 Extracted agentId:', agentId);

    if (!agentId) {
      console.error('❌ Agent ID not provided in URL');
      return new Response(
        JSON.stringify({
          error: 'Agent ID not provided',
          details: 'Agent ID is required in the URL path'
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    console.log('✅ Using agent ID:', agentId);

    // 使用优化的验证逻辑
    console.log('🔍 Validating agent with optimized logic...');
    const { isValid, agentInfo } = await validateAgent(agentId);

    if (!isValid || !agentInfo) {
      console.error(`❌ Agent ${agentId} validation failed`);

      // 只在需要时获取可用agents列表
      let availableAgents: string[] = [];
      try {
        const integrationList = await getIntegrationList();
        availableAgents = integrationList.map((a) => a.id);
      } catch (error) {
        console.warn('Failed to get available agents list:', error);
        availableAgents = Array.from(KNOWN_AGENTS);
      }

      return new Response(
        JSON.stringify({
          error: 'Agent not found',
          details: `Agent ${agentId} is not available`,
          requestedAgent: agentId,
          availableAgents
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    console.log('✅ Agent validation successful:', agentInfo.name || agentInfo.id);

    // 从注册中心获取指定的 Mastra agent 实例
    console.log('🚀 Creating Mastra agent instance...');
    const mastraAgent = await createAgent(agentId);
    console.log('✅ Retrieved Mastra agent instance');
    console.log('📝 Agent basic info:', {
      name: mastraAgent.name,
      hasInstructions: !!mastraAgent.instructions,
      instructionsLength: mastraAgent.instructions?.length || 0,
      hasTools: !!mastraAgent.tools,
      toolsCount: mastraAgent.tools ? Object.keys(mastraAgent.tools).length : 0,
      hasModel: !!mastraAgent.model,
      agentType: typeof mastraAgent,
      agentConstructor: mastraAgent.constructor?.name
    });

    // 详细检查agent属性
    console.log('🔍 Detailed agent inspection:');
    console.log('- Agent name:', mastraAgent.name);
    console.log('- Agent instructions preview:', mastraAgent.instructions?.substring(0, 200) + '...');
    console.log('- Agent tools:', mastraAgent.tools ? Object.keys(mastraAgent.tools) : 'No tools');
    console.log('- Agent model info:', mastraAgent.model ? 'Model present' : 'No model');

    // 创建 Mastra 实例包含我们的 agent
    console.log('🏗️ Creating Mastra instance...');
    const mastra = new Mastra({
      agents: { [agentId]: mastraAgent }
    });

    console.log('✅ Mastra instance created');
    console.log('📋 Mastra agents info:', {
      agentKeys: Object.keys((mastra as any).agents || {}),
      agentCount: Object.keys((mastra as any).agents || {}).length,
      hasTargetAgent: !!((mastra as any).agents && (mastra as any).agents[agentId])
    });

    // 创建运行时上下文
    console.log('🔧 Creating runtime context...');
    const runtimeContext = new Map();
    runtimeContext.set('user-id', 'default-user');
    runtimeContext.set('agent-id', agentId);
    runtimeContext.set('registry', {}); // 添加required registry属性
    console.log('✅ Runtime context created with keys:', Array.from(runtimeContext.keys()));

    // 将 Mastra agent 转换为 CopilotKit 兼容格式
    console.log('🔄 Converting Mastra agent to CopilotKit format...');
    console.log('🔧 MastraAgent.getLocalAgents input parameters:', {
      resourceId: agentId,
      mastraAgentsCount: Object.keys((mastra as any).agents || {}).length,
      runtimeContextKeys: Array.from(runtimeContext.keys())
    });

    let copilotAgents;
    try {
      copilotAgents = MastraAgent.getLocalAgents({
        resourceId: agentId,
        mastra,
        runtimeContext: runtimeContext as any
      });
      console.log('✅ MastraAgent.getLocalAgents completed successfully');
    } catch (conversionError) {
      console.error('❌ Error in MastraAgent.getLocalAgents:', conversionError);
      console.error('Conversion error details:', {
        message: conversionError instanceof Error ? conversionError.message : 'Unknown error',
        stack: conversionError instanceof Error ? conversionError.stack : 'No stack trace'
      });
      throw conversionError;
    }

    console.log('📊 Converted agents info:', {
      agentsCount: Object.keys(copilotAgents || {}).length,
      agentsArray: Array.isArray(copilotAgents),
      agentsType: typeof copilotAgents,
      agentKeys: Object.keys(copilotAgents || {})
    });

    const agentValues = Object.values(copilotAgents || {});
    if (agentValues.length > 0) {
      console.log('📝 First agent details:', {
        name: agentValues[0]?.agentId,
        agentType: agentValues[0]?.constructor?.name,
        agentKeys: Object.keys(agentValues[0] || {})
      });
    } else {
      console.warn('⚠️ No agents returned from conversion or invalid format');
    }

    // 创建 CopilotRuntime，添加 agents
    console.log('🏗️ Creating CopilotRuntime with agents...');
    console.log('🔧 CopilotRuntime input:', {
      agentsProvided: !!copilotAgents,
      agentsCount: Object.keys(copilotAgents || {}).length,
      agentsIsRecord: typeof copilotAgents === 'object' && !Array.isArray(copilotAgents)
    });

    let runtime;
    try {
      runtime = new CopilotRuntime({
        agents: copilotAgents
      });
      console.log('✅ CopilotRuntime created successfully');
    } catch (runtimeError) {
      console.error('❌ Error creating CopilotRuntime:', runtimeError);
      console.error('Runtime error details:', {
        message: runtimeError instanceof Error ? runtimeError.message : 'Unknown error',
        stack: runtimeError instanceof Error ? runtimeError.stack : 'No stack trace'
      });
      throw runtimeError;
    }

    console.log('🎯 CopilotRuntime created with', Object.keys(copilotAgents || {}).length, 'agents for:', agentId);

    // 创建endpoint handler
    console.log('🔧 Creating copilotRuntimeNextJSAppRouterEndpoint...');
    let handleRequest;
    try {
      const endpointResult = copilotRuntimeNextJSAppRouterEndpoint({
        runtime,
        serviceAdapter,
        endpoint: `/api/integration/copilotkit/${agentId}`
      });
      handleRequest = endpointResult.handleRequest;
      console.log('✅ Endpoint handler created successfully');
    } catch (endpointError) {
      console.error('❌ Error creating endpoint handler:', endpointError);
      console.error('Endpoint error details:', {
        message: endpointError instanceof Error ? endpointError.message : 'Unknown error',
        stack: endpointError instanceof Error ? endpointError.stack : 'No stack trace'
      });
      throw endpointError;
    }

    console.log('🎯 About to handle request for agent:', agentId);
    const response = await handleRequest(request);
    console.log('✅ Request handled successfully, response status:', response.status);

    return response;
  } catch (error) {
    console.error('💥 CopilotKit API error:', error);
    console.error('🔍 Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      name: error instanceof Error ? error.name : 'Unknown error type',
      cause: error instanceof Error ? error.cause : undefined
    });

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        details: errorMessage,
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
        agentId: request.url.split('/').pop()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
};
