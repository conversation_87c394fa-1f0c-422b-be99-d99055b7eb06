'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useUserAuth } from '@/hooks/use-user-auth';
import { useAgentPerformance } from '@/hooks/use-performance';
import { AuthLoadingTerminal } from '@workspace/ui/components/loading-terminal';
import { useAgentSession } from '@/hooks/use-agent-mode';

interface AgentClientPageProps {
  agentId: string;
  agentName: string;
}

/**
 * 优化的客户端 Agent 页面组件
 * 自动创建新会话并重定向到带UUID的URL
 * 支持快速重试和错误恢复
 */
export default function AgentClientPage({ agentId, agentName }: AgentClientPageProps) {
  const router = useRouter();
  const { isLoggedIn, username, isLoading } = useUserAuth();
  const { measureAgentValidation, measureSessionCreation } = useAgentPerformance();
  const [isCreatingSession, setIsCreatingSession] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const { isSpecialMode, displayName, agentConfig } = useAgentSession(agentId);

  // 优化的会话创建函数，支持重试
  const createNewSession = useCallback(async () => {
    // 等待认证状态确定
    if (isLoading) {
      return;
    }

    // 检查用户是否已登录
    if (!isLoggedIn) {
      console.error(`用户未登录，无法创建${displayName}会话`);
      // 重定向到首页，用户需要先登录
      router.replace('/');
      return;
    }

    try {
      // 设置创建会话状态
      setIsCreatingSession(true);
      setError(null);

      // 预加载agent配置，确保agent可用（带性能监控）
      await measureAgentValidation(agentId, async () => {
        const agentCheckResponse = await fetch(`/api/agents?fast=true`);
        if (agentCheckResponse.ok) {
          const agentsData = await agentCheckResponse.json();
          const agentExists = agentsData.agents?.some((agent: any) => agent.id === agentId);

          if (!agentExists) {
            throw new Error(`Agent ${agentId} 不可用`);
          }
        }
        return true;
      });

      // 创建新会话（带性能监控）
      const response = await measureSessionCreation(agentId, async () => {
        return fetch('/api/agent-session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            agentId,
            agentName: displayName,
            title: `${displayName}对话 - ${new Date().toLocaleString('zh-CN')}`,
            username: username || '',
            mode: agentConfig.integrationType,
            agentConfig: agentConfig.customConfig
          })
        });
      });

      if (response.ok) {
        const data = await response.json();
        // 重定向到带UUID的URL
        router.replace(`/${agentId}/${data.sessionId}`);
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to create ${displayName} session`);
      }
    } catch (error) {
      console.error(`Error creating ${displayName} session:`, error);
      setError(error instanceof Error ? error.message : '创建会话失败');
      setIsCreatingSession(false);

      // 自动重试逻辑（最多3次）
      if (retryCount < 3) {
        console.log(`Retrying session creation (attempt ${retryCount + 1}/3)...`);
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
        }, 1000 * (retryCount + 1)); // 递增延迟
      }
    }
  }, [agentId, agentName, router, isLoggedIn, username, isLoading, displayName, agentConfig, retryCount]);

  // 主要的effect，处理会话创建
  useEffect(() => {
    createNewSession();
  }, [agentId, agentName, router, isLoggedIn, username, isLoading, displayName, agentConfig, retryCount, measureAgentValidation, measureSessionCreation]);

  // 重试effect
  useEffect(() => {
    if (retryCount > 0 && retryCount <= 3) {
      createNewSession();
    }
  }, [retryCount, createNewSession]);

  // 显示加载状态
  return (
    <AuthLoadingTerminal
      agentName={displayName}
      isLoading={isLoading}
      isCreatingSession={isCreatingSession}
      error={error}
      retryCount={retryCount}
    />
  );
}
