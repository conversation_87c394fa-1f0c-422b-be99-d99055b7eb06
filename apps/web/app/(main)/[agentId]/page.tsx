import React from 'react';
import { notFound } from 'next/navigation';
import AgentClientPage from './client-page';

// 预定义的已知agents，用于快速验证和fallback
const KNOWN_AGENTS = new Set(['haiku', 'mario']);

// 缓存的agent信息，避免重复API调用
let cachedAgentList: string[] | null = null;
let cacheTimestamp = 0;
const CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

/**
 * 获取可用的agent列表（带缓存）
 */
async function getAvailableAgents(): Promise<string[]> {
  const now = Date.now();

  // 如果缓存有效，直接返回
  if (cachedAgentList && (now - cacheTimestamp) < CACHE_TTL) {
    return cachedAgentList;
  }

  try {
    // 尝试从API获取最新的agent列表
    const { getIntegrationList } = await import('@workspace/agent-registry/server');
    const integrations = await getIntegrationList();

    if (integrations && integrations.length > 0) {
      cachedAgentList = integrations.map(integration => integration.id);
      cacheTimestamp = now;
      return cachedAgentList;
    }
  } catch (error) {
    console.warn('Failed to fetch agent list, using fallback:', error);
  }

  // Fallback到已知的agents
  cachedAgentList = Array.from(KNOWN_AGENTS);
  cacheTimestamp = now;
  return cachedAgentList;
}

/**
 * 生成静态参数，用于构建时预渲染所有可用的 agent 页面
 */
export async function generateStaticParams() {
  try {
    const availableAgents = await getAvailableAgents();
    console.log('generateStaticParams - available agents:', availableAgents);

    return availableAgents.map((agentId) => ({
      agentId
    }));
  } catch (error) {
    console.error('Error in generateStaticParams:', error);
    return Array.from(KNOWN_AGENTS).map(agentId => ({ agentId }));
  }
}

interface AgentPageProps {
  params: Promise<{
    agentId: string;
  }>;
}

/**
 * 优化的服务端 Agent 页面组件
 * 使用快速验证和缓存机制减少加载时间
 */
export default async function AgentPage({ params }: AgentPageProps) {
  const { agentId } = await params;

  // 快速验证：首先检查是否是已知的agent
  if (KNOWN_AGENTS.has(agentId)) {
    // 对于已知agents，直接渲染，减少验证时间
    return <AgentClientPage agentId={agentId} agentName={getAgentDisplayName(agentId)} />;
  }

  // 对于未知agents，进行完整验证
  try {
    const availableAgents = await getAvailableAgents();

    if (!availableAgents.includes(agentId)) {
      notFound();
    }

    // 尝试获取agent详细信息
    let agentName = getAgentDisplayName(agentId);
    try {
      const { getIntegration } = await import('@workspace/agent-registry/server');
      const agentPackage = await getIntegration(agentId);
      if (agentPackage?.name) {
        agentName = agentPackage.name;
      }
    } catch (error) {
      console.warn(`Failed to get agent details for ${agentId}, using fallback name:`, error);
    }

    return <AgentClientPage agentId={agentId} agentName={agentName} />;
  } catch (error) {
    console.error(`Error validating agent ${agentId}:`, error);
    notFound();
  }
}

/**
 * 获取agent的显示名称
 */
function getAgentDisplayName(agentId: string): string {
  const displayNames: Record<string, string> = {
    'haiku': 'Haiku Agent',
    'mario': 'Mario Agent'
  };

  return displayNames[agentId] || `${agentId.charAt(0).toUpperCase() + agentId.slice(1)} Agent`;
}
