'use client';

import { useEffect, ReactNode } from 'react';
import { initializeOptimization } from '@/lib/agent-optimization';

interface OptimizationProviderProps {
  children: ReactNode;
}

/**
 * 优化系统提供者组件
 * 负责初始化agent预加载和性能监控
 */
export function OptimizationProvider({ children }: OptimizationProviderProps) {
  useEffect(() => {
    // 在客户端初始化优化系统
    initializeOptimization();
  }, []);

  return <>{children}</>;
}
