# Agent 选择优化方案

## 问题分析

原有的agent选择逻辑存在以下性能问题：

1. **重复验证**：每次访问都会调用多个API进行agent验证
2. **缺乏缓存**：没有对agent信息进行有效缓存
3. **串行处理**：验证和加载过程完全串行，没有并行优化
4. **错误处理不足**：缺乏重试机制和优雅降级

## 优化方案

### 1. 智能缓存机制

#### 服务端缓存
- **Agent列表缓存**：3分钟TTL，避免重复查询注册中心
- **Agent验证缓存**：2分钟TTL，缓存验证结果
- **分层缓存策略**：内存缓存 + 降级策略

#### 客户端缓存
- **预加载机制**：启动时预加载高优先级agents
- **智能预热**：提前预热CopilotKit端点

### 2. 快速验证策略

#### 已知Agent快速通道
```typescript
const KNOWN_AGENTS = new Set(['haiku', 'mario']);

// 对于已知agents，跳过复杂验证
if (KNOWN_AGENTS.has(agentId)) {
  return <AgentClientPage agentId={agentId} agentName={getAgentDisplayName(agentId)} />;
}
```

#### 分层验证
1. **快速检查**：检查是否为已知agent
2. **缓存查询**：查询缓存的验证结果
3. **完整验证**：仅在必要时进行完整验证

### 3. 性能监控

#### 请求性能追踪
- 记录每个操作的响应时间
- 识别慢请求并告警
- 提供性能统计面板

#### 智能重试机制
- 自动重试失败的请求（最多3次）
- 递增延迟策略
- 优雅的错误处理

### 4. API优化

#### 快速模式API
```typescript
// 快速模式：立即返回基础agents
GET /api/agents?fast=true

// 完整模式：从注册中心获取最新数据
GET /api/agents?full=true
```

#### 批量验证
- 减少API调用次数
- 并行处理多个验证请求
- 智能合并相似请求

## 实现细节

### 1. 页面级优化

#### 服务端组件优化
```typescript
// apps/web/app/(main)/[agentId]/page.tsx
export default async function AgentPage({ params }: AgentPageProps) {
  const { agentId } = await params;

  // 快速验证：首先检查是否是已知的agent
  if (KNOWN_AGENTS.has(agentId)) {
    return <AgentClientPage agentId={agentId} agentName={getAgentDisplayName(agentId)} />;
  }

  // 对于未知agents，进行缓存验证
  const availableAgents = await getAvailableAgents(); // 带缓存
  // ...
}
```

#### 客户端组件优化
```typescript
// 支持重试和错误恢复
const createNewSession = useCallback(async () => {
  // 预加载agent配置（带性能监控）
  await measureAgentValidation(agentId, async () => {
    // 验证逻辑
  });

  // 创建会话（带性能监控）
  const response = await measureSessionCreation(agentId, async () => {
    // 会话创建逻辑
  });
}, [agentId, measureAgentValidation, measureSessionCreation]);
```

### 2. API级优化

#### 智能缓存
```typescript
// 缓存的agent验证结果
const agentValidationCache = new Map<string, { isValid: boolean; timestamp: number; agentInfo?: any }>();

async function validateAgent(agentId: string): Promise<{ isValid: boolean; agentInfo?: any }> {
  // 检查缓存
  const cached = agentValidationCache.get(agentId);
  if (cached && (Date.now() - cached.timestamp) < VALIDATION_CACHE_TTL) {
    return { isValid: cached.isValid, agentInfo: cached.agentInfo };
  }

  // 对于已知agents，快速验证
  if (KNOWN_AGENTS.has(agentId)) {
    // 快速通道逻辑
  }

  // 完整验证逻辑
}
```

### 3. 预加载系统

#### Agent预加载器
```typescript
export class AgentPreloader {
  async preloadAgents(agentIds: string[] = HIGH_PRIORITY_AGENTS): Promise<void> {
    const preloadPromises = agentIds.map(agentId => this.preloadAgent(agentId));
    await Promise.allSettled(preloadPromises);
  }

  private async preloadAgent(agentId: string): Promise<void> {
    // 预加载agent配置
    // 预热CopilotKit端点
  }
}
```

### 4. 性能监控

#### 性能指标收集
```typescript
export class PerformanceMonitor {
  recordRequest(operation: string, duration: number): void {
    // 记录性能指标
    // 检测慢请求
    // 生成统计报告
  }
}
```

## 性能提升效果

### 预期改进

1. **首次加载时间**：减少50-70%
   - 已知agents：从3-5秒降至1-2秒
   - 未知agents：从5-8秒降至2-3秒

2. **重复访问**：减少80-90%
   - 缓存命中：响应时间<500ms
   - 预加载命中：响应时间<200ms

3. **错误恢复**：提升用户体验
   - 自动重试机制
   - 优雅降级策略
   - 详细的错误信息

### 监控指标

- **响应时间分布**：P50, P90, P99
- **缓存命中率**：>80%
- **错误率**：<5%
- **重试成功率**：>90%

## 使用方法

### 1. 启用优化系统

优化系统会在应用启动时自动初始化：

```typescript
// 在 OptimizationProvider 中自动启用
useEffect(() => {
  initializeOptimization();
}, []);
```

### 2. 查看性能统计

```typescript
import { getOptimizationStats } from '@/lib/agent-optimization';

const stats = getOptimizationStats();
console.log('优化统计:', stats);
```

### 3. 手动预加载

```typescript
import { AgentPreloader } from '@/lib/agent-optimization';

const preloader = AgentPreloader.getInstance();
await preloader.preloadAgents(['custom-agent']);
```

## 配置选项

### 缓存配置
```typescript
export const CACHE_CONFIG = {
  agentListTTL: 3 * 60 * 1000,     // 3分钟
  agentDetailTTL: 5 * 60 * 1000,   // 5分钟
  validationTTL: 2 * 60 * 1000,    // 2分钟
  maxEntries: 50
};
```

### 预加载配置
```typescript
export const PRELOAD_CONFIG = {
  enabled: true,
  delay: 1000,                      // 1秒延迟启动
  agents: ['haiku', 'mario'],       // 预加载的agents
  timeout: 5000                     // 5秒超时
};
```

### 性能监控配置
```typescript
export const PERFORMANCE_CONFIG = {
  enabled: true,
  slowRequestThreshold: 2000,       // 2秒慢请求阈值
  sampleRate: 0.1                   // 10%采样率
};
```
