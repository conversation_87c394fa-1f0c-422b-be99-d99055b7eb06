import { useState, useEffect, useRef } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';

/**
 * Agent 集成类型枚举
 */
export enum AgentIntegrationType {
  COPILOTKIT = 'copilotkit',
  MARIO = 'mario',
  CUSTOM = 'custom'
}

/**
 * Agent 基础信息接口
 */
export interface AgentInfo {
  id: string;
  name: string;
  description: string;
  version?: string;
  route?: string;
  type?: string;
  originalId?: string;
}

/**
 * 预定义的agents信息（从integrations目录获取）
 */
const PREDEFINED_AGENTS: AgentInfo[] = [
  {
    id: 'haiku',
    name: 'Haiku Integration',
    description: '专业的诗词生成助手，能够创作优美的中英双语诗词',
    version: '0.0.1',
    route: '/haiku',
    type: 'integration',
    originalId: 'haiku'
  },
  {
    id: 'mario',
    name: 'Mario Integration',
    description: 'Mairo测试框架，能够自动生成EC测试用例和Thrift泛化测试用例',
    version: '0.0.1',
    route: '/mario',
    type: 'integration',
    originalId: 'mario'
  }
];

/**
 * Agent 配置接口（扩展配置）
 */
export interface AgentConfig {
  /** Agent 标识符 */
  id: string;
  /** 显示名称 */
  displayName: string;
  /** 描述 */
  description?: string;
  /** 集成类型 */
  integrationType: AgentIntegrationType;
  /** API 端点路径 */
  apiEndpoint: string;
  /** 是否需要特殊处理 */
  requiresSpecialHandling: boolean;
  /** 路由路径 */
  route?: string;
  /** 自定义配置 */
  customConfig?: Record<string, any>;
}

/**
 * Agent 运行时状态接口
 */
export interface AgentRuntimeState {
  agentConfig: AgentConfig;
  isSpecialMode: boolean;
  integrationType: AgentIntegrationType;
  displayName: string;
  requiresSpecialHandling: boolean;
  apiRoute: string | null;
  sessionTitle: (baseName: string) => string;
}

/**
 * Agent 集成配置接口
 * 每个 Agent 应该导出这个配置
 */
export interface AgentIntegrationConfig {
  /** 集成类型 */
  integrationType: AgentIntegrationType;
  /** API 端点路径 */
  apiEndpoint: string;
  /** 是否需要特殊处理 */
  requiresSpecialHandling: boolean;
  /** 显示名称模板 */
  displayNameTemplate?: string;
  /** 自定义配置 */
  customConfig?: Record<string, any>;
}

/**
 * 默认配置 - 未知 Agent 使用此配置
 */
const DEFAULT_AGENT_CONFIG: AgentIntegrationConfig = {
  integrationType: AgentIntegrationType.COPILOTKIT,
  apiEndpoint: '/api/integration/copilotkit',
  requiresSpecialHandling: false
};

/**
 * 根据 Agent ID 推断集成配置
 * 基于约定优于配置的原则
 */
export function getAgentIntegrationConfig(agentId: string): AgentIntegrationConfig {
  // 基于 Agent ID 推断集成类型
  if (agentId === 'mario' || agentId.includes('mario')) {
    return {
      integrationType: AgentIntegrationType.MARIO,
      apiEndpoint: '/api/integration/mario',
      requiresSpecialHandling: true,
      displayNameTemplate: 'Mario {name}',
      customConfig: {
        websocketEndpoint: 'ws://localhost:8080/ws/ag-ui',
        supportedFeatures: ['canvas', 'testing', 'visualization', 'automation', 'docker'],
        dockerSupport: true,
        terminalSupport: true,
        codeGeneration: true,
        testFramework: 'TestNG',
        languages: ['Java', 'Python'],
        protocols: ['HTTP', 'Thrift', 'gRPC']
      }
    };
  }

  if (agentId === 'haiku' || agentId.includes('haiku')) {
    return {
      integrationType: AgentIntegrationType.COPILOTKIT,
      apiEndpoint: '/api/integration/copilotkit',
      requiresSpecialHandling: false,
      displayNameTemplate: '{name}',
      customConfig: {
        supportedFeatures: ['poetry', 'creative-writing', 'chinese-poetry'],
        language: 'zh-CN',
        model: 'gpt-4o'
      }
    };
  }

  // 默认为 CopilotKit 集成
  return DEFAULT_AGENT_CONFIG;
}

/**
 * 根据 Agent 信息生成完整配置
 */
export function generateAgentConfig(agentInfo: AgentInfo): AgentConfig {
  const integrationConfig = getAgentIntegrationConfig(agentInfo.id);

  // 生成显示名称
  let displayName = agentInfo.name;
  if (integrationConfig.displayNameTemplate) {
    displayName = integrationConfig.displayNameTemplate.replace('{name}', agentInfo.name);
  }

  return {
    id: agentInfo.id,
    displayName,
    description: agentInfo.description,
    integrationType: integrationConfig.integrationType,
    apiEndpoint: integrationConfig.apiEndpoint,
    requiresSpecialHandling: integrationConfig.requiresSpecialHandling,
    route: agentInfo.route || `/${agentInfo.id}`,
    customConfig: integrationConfig.customConfig
  };
}

/**
 * 获取 Agent 配置（兼容旧接口）
 */
export function getAgentConfig(agentId: string): AgentConfig {
  // 创建基础 AgentInfo
  const agentInfo: AgentInfo = {
    id: agentId,
    name: agentId,
    description: `Agent ${agentId}`
  };

  return generateAgentConfig(agentInfo);
}

/**
 * 统一的 Agent 管理 Hook
 * 提供 Agent 列表获取、配置管理、模式检测等功能
 */
export function useAgents() {
  const [agents, setAgents] = useState<AgentInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const fetchedRef = useRef(false);
  const fastFetchedRef = useRef(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 获取预定义的agents
  const fetchAgentsFast = async () => {
    if (fastFetchedRef.current) return;

    try {
      // 直接使用预定义的agents信息
      setAgents(PREDEFINED_AGENTS);
      setLoading(false);
      fastFetchedRef.current = true;
      console.log('Predefined agents loaded successfully:', PREDEFINED_AGENTS.length);
    } catch (err) {
      console.error('Failed to load predefined agents:', err);
    }
  };

  // 完整获取agents数据
  const fetchAgentsFull = async () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    try {
      setError(null);

      const response = await fetch('/api/agents?full=true', {
        signal: abortController.signal
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch full agents: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.agents && Array.isArray(data.agents)) {
        setAgents(data.agents);
        fetchedRef.current = true;
        console.log('Full agents fetched successfully:', data.agents.length, 'mode:', data.mode);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        console.log('Fetch full agents request was aborted');
        return;
      }

      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Error fetching full agents:', err);

      if (!fastFetchedRef.current) {
        setLoading(false);
      }
    } finally {
      abortControllerRef.current = null;
    }
  };

  // 组合的获取函数
  const fetchAgents = async () => {
    await fetchAgentsFast();
    await fetchAgentsFull();
  };

  useEffect(() => {
    if (!fetchedRef.current) {
      fetchAgents();
    }

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // 生成带配置的 Agent 列表
  const agentsWithConfig = agents.map(agent => ({
    ...agent,
    config: generateAgentConfig(agent)
  }));

  return {
    agents: agentsWithConfig,
    loading,
    error,
    refetch: fetchAgents,
    // 辅助方法
    getAgentById: (id: string) => agentsWithConfig.find(a => a.id === id),
    getAgentsByType: (type: AgentIntegrationType) =>
      agentsWithConfig.filter(a => a.config.integrationType === type)
  };
}

/**
 * Agent 模式检测 Hook
 * 根据路径、查询参数和 Agent 配置判断模式
 */
export function useAgentMode(agentId: string): AgentRuntimeState {
  const pathname = usePathname();
  const agentConfig = getAgentConfig(agentId);

  // 安全地获取查询参数，避免 SSR 问题
  let searchParams: URLSearchParams | null = null;
  try {
    searchParams = useSearchParams();
  } catch (error) {
    // 在 SSR 环境中忽略 searchParams
    console.warn('useSearchParams not available in SSR context');
  }

  // 检测特殊模式
  const isSpecialMode = pathname.includes(`/${agentConfig.integrationType}`) ||
                       (searchParams && searchParams.get('mode') === agentConfig.integrationType) ||
                       agentConfig.requiresSpecialHandling;

  return {
    agentConfig,
    isSpecialMode,
    integrationType: agentConfig.integrationType,
    displayName: agentConfig.displayName,
    requiresSpecialHandling: agentConfig.requiresSpecialHandling,
    apiRoute: null, // 需要 sessionId 才能生成
    sessionTitle: (baseName: string) =>
      isSpecialMode ? `${agentConfig.displayName} ${baseName}` : baseName
  };
}

/**
 * Agent 会话管理 Hook
 * 提供完整的会话配置和 API 路由
 */
export function useAgentSession(agentId: string, sessionId?: string): AgentRuntimeState {
  const runtimeState = useAgentMode(agentId);

  // 生成 API 路由
  const apiRoute = sessionId
    ? `${runtimeState.agentConfig.apiEndpoint}/${agentId}?sessionId=${sessionId}`
    : null;

  return {
    ...runtimeState,
    apiRoute
  };
}

/**
 * 根据 agent ID 获取对应的路由路径
 * 兼容旧的路由格式
 */
export function getAgentRoute(agentId: string, agents?: Array<AgentInfo & { config: AgentConfig }>): string {
  if (agents) {
    const agent = agents.find((a) => a.id === agentId);
    if (agent?.route) {
      return agent.route;
    }
    if (agent?.config.route) {
      return agent.config.route;
    }
  }

  // 对于新的参数化路由，直接使用 agentId
  const cleanId = agentId.replace(/Agent$/, '').toLowerCase();
  return `/${cleanId}`;
}

/**
 * 获取所有已知的 Agent 配置
 */
export function getAllRegisteredConfigs(): Map<string, AgentIntegrationConfig> {
  const knownAgents = ['mario', 'haiku'];
  const configs = new Map<string, AgentIntegrationConfig>();

  for (const agentId of knownAgents) {
    configs.set(agentId, getAgentIntegrationConfig(agentId));
  }

  return configs;
}

/**
 * 根据集成类型获取 Agent 配置
 */
export function getConfigsByType(type: AgentIntegrationType): Array<{ agentId: string; config: AgentIntegrationConfig }> {
  // 这个函数现在基于约定返回已知的配置
  const knownAgents = ['mario', 'haiku'];
  const result: Array<{ agentId: string; config: AgentIntegrationConfig }> = [];

  for (const agentId of knownAgents) {
    const config = getAgentIntegrationConfig(agentId);
    if (config.integrationType === type) {
      result.push({ agentId, config });
    }
  }

  return result;
}

/**
 * 智能 API 路由选择函数
 * 根据 Agent 配置自动选择正确的 API 端点
 */
export function useApiRoute(agentId: string, sessionId: string) {
  const { agentConfig, apiRoute } = useAgentSession(agentId, sessionId);

  return {
    apiRoute: apiRoute!,
    agentConfig,
    integrationType: agentConfig.integrationType
  };
}

// 向后兼容的导出
export { useAgents as useAgentsList };

/**
 * 类型守卫：检查是否为 Mario Agent
 */
export function isMarioAgent(agentId: string): boolean {
  const config = getAgentConfig(agentId);
  return config.integrationType === AgentIntegrationType.MARIO;
}

/**
 * 类型守卫：检查是否为 CopilotKit Agent
 */
export function isCopilotKitAgent(agentId: string): boolean {
  const config = getAgentConfig(agentId);
  return config.integrationType === AgentIntegrationType.COPILOTKIT;
}

/**
 * 类型守卫：检查是否为自定义 Agent
 */
export function isCustomAgent(agentId: string): boolean {
  const config = getAgentConfig(agentId);
  return config.integrationType === AgentIntegrationType.CUSTOM;
}
