'use client';

import { useCallback, useEffect, useState } from 'react';
import { PerformanceMonitor, getOptimizationStats } from '@/lib/agent-optimization';

/**
 * 性能监控 Hook
 */
export function usePerformance() {
  const [stats, setStats] = useState<any>(null);
  const monitor = PerformanceMonitor.getInstance();

  /**
   * 记录操作性能
   */
  const recordOperation = useCallback((operation: string, startTime: number) => {
    const duration = Date.now() - startTime;
    monitor.recordRequest(operation, duration);
  }, [monitor]);

  /**
   * 测量异步操作性能
   */
  const measureAsync = useCallback(async <T>(
    operation: string,
    asyncFn: () => Promise<T>
  ): Promise<T> => {
    const startTime = Date.now();
    try {
      const result = await asyncFn();
      recordOperation(operation, startTime);
      return result;
    } catch (error) {
      recordOperation(`${operation}_error`, startTime);
      throw error;
    }
  }, [recordOperation]);

  /**
   * 测量同步操作性能
   */
  const measureSync = useCallback(<T>(
    operation: string,
    syncFn: () => T
  ): T => {
    const startTime = Date.now();
    try {
      const result = syncFn();
      recordOperation(operation, startTime);
      return result;
    } catch (error) {
      recordOperation(`${operation}_error`, startTime);
      throw error;
    }
  }, [recordOperation]);

  /**
   * 刷新统计信息
   */
  const refreshStats = useCallback(() => {
    setStats(getOptimizationStats());
  }, []);

  /**
   * 获取特定操作的统计信息
   */
  const getOperationStats = useCallback((operation: string) => {
    return monitor.getStats(operation);
  }, [monitor]);

  useEffect(() => {
    // 定期刷新统计信息
    const interval = setInterval(refreshStats, 30000); // 30秒
    refreshStats(); // 立即刷新一次

    return () => clearInterval(interval);
  }, [refreshStats]);

  return {
    stats,
    recordOperation,
    measureAsync,
    measureSync,
    refreshStats,
    getOperationStats
  };
}

/**
 * Agent 加载性能监控 Hook
 */
export function useAgentPerformance() {
  const { measureAsync, getOperationStats } = usePerformance();

  /**
   * 测量 agent 验证性能
   */
  const measureAgentValidation = useCallback((agentId: string, validationFn: () => Promise<any>) => {
    return measureAsync(`agent_validation_${agentId}`, validationFn);
  }, [measureAsync]);

  /**
   * 测量 agent 会话创建性能
   */
  const measureSessionCreation = useCallback((agentId: string, creationFn: () => Promise<any>) => {
    return measureAsync(`session_creation_${agentId}`, creationFn);
  }, [measureAsync]);

  /**
   * 测量 CopilotKit 初始化性能
   */
  const measureCopilotInit = useCallback((agentId: string, initFn: () => Promise<any>) => {
    return measureAsync(`copilot_init_${agentId}`, initFn);
  }, [measureAsync]);

  /**
   * 获取 agent 相关的性能统计
   */
  const getAgentStats = useCallback((agentId: string) => {
    return {
      validation: getOperationStats(`agent_validation_${agentId}`),
      sessionCreation: getOperationStats(`session_creation_${agentId}`),
      copilotInit: getOperationStats(`copilot_init_${agentId}`)
    };
  }, [getOperationStats]);

  return {
    measureAgentValidation,
    measureSessionCreation,
    measureCopilotInit,
    getAgentStats
  };
}
