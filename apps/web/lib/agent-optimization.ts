/**
 * Agent 优化配置和工具函数
 * 提供agent预加载、缓存和性能优化功能
 */

// 预定义的高优先级agents
export const HIGH_PRIORITY_AGENTS = ['haiku', 'mario'];

// 预加载配置
export const PRELOAD_CONFIG = {
  // 是否启用预加载
  enabled: true,
  // 预加载延迟（毫秒）
  delay: 1000,
  // 预加载的agents
  agents: HIGH_PRIORITY_AGENTS,
  // 预加载超时时间
  timeout: 5000
};

// 缓存配置
export const CACHE_CONFIG = {
  // agents列表缓存时间（毫秒）
  agentListTTL: 3 * 60 * 1000, // 3分钟
  // agent详情缓存时间（毫秒）
  agentDetailTTL: 5 * 60 * 1000, // 5分钟
  // 验证结果缓存时间（毫秒）
  validationTTL: 2 * 60 * 1000, // 2分钟
  // 最大缓存条目数
  maxEntries: 50
};

// 性能监控配置
export const PERFORMANCE_CONFIG = {
  // 是否启用性能监控
  enabled: true,
  // 慢请求阈值（毫秒）
  slowRequestThreshold: 2000,
  // 采样率（0-1）
  sampleRate: 0.1
};

/**
 * Agent预加载器
 */
export class AgentPreloader {
  private static instance: AgentPreloader;
  private preloadedAgents = new Set<string>();
  private preloadPromises = new Map<string, Promise<any>>();

  static getInstance(): AgentPreloader {
    if (!AgentPreloader.instance) {
      AgentPreloader.instance = new AgentPreloader();
    }
    return AgentPreloader.instance;
  }

  /**
   * 预加载指定的agents
   */
  async preloadAgents(agentIds: string[] = HIGH_PRIORITY_AGENTS): Promise<void> {
    if (!PRELOAD_CONFIG.enabled) {
      return;
    }

    console.log('🚀 Starting agent preload for:', agentIds);

    const preloadPromises = agentIds.map(agentId => this.preloadAgent(agentId));
    
    try {
      await Promise.allSettled(preloadPromises);
      console.log('✅ Agent preload completed');
    } catch (error) {
      console.warn('⚠️ Some agents failed to preload:', error);
    }
  }

  /**
   * 预加载单个agent
   */
  private async preloadAgent(agentId: string): Promise<void> {
    if (this.preloadedAgents.has(agentId)) {
      return;
    }

    // 避免重复预加载
    if (this.preloadPromises.has(agentId)) {
      return this.preloadPromises.get(agentId);
    }

    const preloadPromise = this.doPreloadAgent(agentId);
    this.preloadPromises.set(agentId, preloadPromise);

    try {
      await preloadPromise;
      this.preloadedAgents.add(agentId);
      console.log(`✅ Preloaded agent: ${agentId}`);
    } catch (error) {
      console.warn(`⚠️ Failed to preload agent ${agentId}:`, error);
    } finally {
      this.preloadPromises.delete(agentId);
    }
  }

  /**
   * 执行实际的预加载逻辑
   */
  private async doPreloadAgent(agentId: string): Promise<void> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), PRELOAD_CONFIG.timeout);

    try {
      // 预加载agent配置
      const configResponse = await fetch(`/api/agents?fast=true`, {
        signal: controller.signal
      });

      if (configResponse.ok) {
        const data = await configResponse.json();
        const agentExists = data.agents?.some((agent: any) => agent.id === agentId);
        
        if (!agentExists) {
          throw new Error(`Agent ${agentId} not found in fast mode`);
        }
      }

      // 预热CopilotKit端点（不等待响应）
      fetch(`/api/integration/copilotkit/${agentId}`, {
        method: 'HEAD',
        signal: controller.signal
      }).catch(() => {
        // 忽略预热错误
      });

    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * 检查agent是否已预加载
   */
  isPreloaded(agentId: string): boolean {
    return this.preloadedAgents.has(agentId);
  }

  /**
   * 清除预加载缓存
   */
  clearCache(): void {
    this.preloadedAgents.clear();
    this.preloadPromises.clear();
  }
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics = new Map<string, number[]>();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * 记录请求性能
   */
  recordRequest(operation: string, duration: number): void {
    if (!PERFORMANCE_CONFIG.enabled) {
      return;
    }

    // 采样
    if (Math.random() > PERFORMANCE_CONFIG.sampleRate) {
      return;
    }

    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }

    const durations = this.metrics.get(operation)!;
    durations.push(duration);

    // 保持最近100个记录
    if (durations.length > 100) {
      durations.shift();
    }

    // 记录慢请求
    if (duration > PERFORMANCE_CONFIG.slowRequestThreshold) {
      console.warn(`🐌 Slow request detected: ${operation} took ${duration}ms`);
    }
  }

  /**
   * 获取性能统计
   */
  getStats(operation: string): { avg: number; min: number; max: number; count: number } | null {
    const durations = this.metrics.get(operation);
    if (!durations || durations.length === 0) {
      return null;
    }

    const avg = durations.reduce((sum, d) => sum + d, 0) / durations.length;
    const min = Math.min(...durations);
    const max = Math.max(...durations);

    return { avg, min, max, count: durations.length };
  }

  /**
   * 获取所有性能统计
   */
  getAllStats(): Record<string, any> {
    const stats: Record<string, any> = {};
    
    for (const [operation, durations] of this.metrics.entries()) {
      if (durations.length > 0) {
        const avg = durations.reduce((sum, d) => sum + d, 0) / durations.length;
        const min = Math.min(...durations);
        const max = Math.max(...durations);
        
        stats[operation] = { avg, min, max, count: durations.length };
      }
    }

    return stats;
  }
}

/**
 * 初始化优化系统
 */
export function initializeOptimization(): void {
  if (typeof window === 'undefined') {
    return; // 仅在客户端运行
  }

  console.log('🔧 Initializing agent optimization system...');

  // 延迟启动预加载
  setTimeout(() => {
    const preloader = AgentPreloader.getInstance();
    preloader.preloadAgents();
  }, PRELOAD_CONFIG.delay);

  console.log('✅ Agent optimization system initialized');
}

/**
 * 获取优化统计信息
 */
export function getOptimizationStats(): any {
  const preloader = AgentPreloader.getInstance();
  const monitor = PerformanceMonitor.getInstance();

  return {
    preload: {
      enabled: PRELOAD_CONFIG.enabled,
      preloadedAgents: Array.from((preloader as any).preloadedAgents),
      highPriorityAgents: HIGH_PRIORITY_AGENTS
    },
    performance: monitor.getAllStats(),
    cache: CACHE_CONFIG
  };
}
