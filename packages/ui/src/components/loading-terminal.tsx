'use client';

import { cn } from '@workspace/ui/lib/utils';
import { motion } from 'motion/react';
import { useEffect, useState } from 'react';

interface AnimatedSpanProps {
  children: React.ReactNode;
  delay?: number;
  className?: string;
}

export const AnimatedSpan = ({ children, delay = 0, className }: AnimatedSpanProps) => (
  <motion.div
    initial={{ opacity: 0, y: -5 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3, delay: delay / 1000 }}
    className={cn('grid font-mono text-sm font-normal tracking-tight text-green-400', className)}>
    {children}
  </motion.div>
);

interface TypingAnimationProps {
  children: string;
  className?: string;
  duration?: number;
  delay?: number;
}

export const TypingAnimation = ({ children, className, duration = 60, delay = 0 }: TypingAnimationProps) => {
  const [displayedText, setDisplayedText] = useState<string>('');
  const [started, setStarted] = useState(false);

  useEffect(() => {
    const startTimeout = setTimeout(() => {
      setStarted(true);
    }, delay);
    return () => clearTimeout(startTimeout);
  }, [delay]);

  useEffect(() => {
    if (!started) return;

    // 确保 children 是字符串类型
    const text = typeof children === 'string' ? children : String(children);

    let i = 0;
    const typingEffect = setInterval(() => {
      if (i < text.length) {
        setDisplayedText(text.substring(0, i + 1));
        i++;
      } else {
        clearInterval(typingEffect);
      }
    }, duration);

    return () => {
      clearInterval(typingEffect);
    };
  }, [children, duration, started]);

  return (
    <span className={cn('font-mono text-sm font-normal tracking-tight text-green-400', className)}>
      {displayedText}
    </span>
  );
};

interface LoadingTerminalProps {
  className?: string;
  children: React.ReactNode;
}

export const LoadingTerminal = ({ children, className }: LoadingTerminalProps) => {
  return (
    <div className='flex h-screen items-center justify-center bg-black/5'>
      <div
        className={cn(
          'z-0 h-full max-h-[400px] w-full max-w-lg rounded-xl border border-gray-700 bg-black shadow-2xl',
          className
        )}>
        <div className='flex flex-col gap-y-2 rounded-t-xl border-b border-gray-700 bg-gray-900 p-4'>
          <div className='flex flex-row gap-x-2'>
            <div className='h-3 w-3 rounded-full bg-red-500 shadow-sm'></div>
            <div className='h-3 w-3 rounded-full bg-yellow-500 shadow-sm'></div>
            <div className='h-3 w-3 rounded-full bg-green-500 shadow-sm'></div>
          </div>
        </div>
        <pre className='bg-black p-4 font-mono text-sm leading-relaxed text-green-400'>
          <code className='grid gap-y-1 overflow-auto text-green-400'>{children}</code>
        </pre>
      </div>
    </div>
  );
};

interface AuthLoadingTerminalProps {
  agentName?: string;
  isLoading: boolean;
  isCreatingSession: boolean;
  error?: string | null;
  retryCount?: number;
}

export const AuthLoadingTerminal = ({
  agentName,
  isLoading,
  isCreatingSession,
  error,
  retryCount = 0
}: AuthLoadingTerminalProps) => {
  return (
    <LoadingTerminal>
      <TypingAnimation>{'> 启动 Muse Studio ...'}</TypingAnimation>

      <AnimatedSpan delay={1000} className='text-green-400'>
        <span>✔ 初始化系统环境</span>
      </AnimatedSpan>

      <AnimatedSpan delay={1500} className='text-green-400'>
        <span>✔ 加载安全模块</span>
      </AnimatedSpan>

      {isLoading && (
        <>
          <AnimatedSpan delay={2000} className='text-yellow-400'>
            <span>⏳ 正在验证用户身份...</span>
          </AnimatedSpan>
        </>
      )}

      {!isLoading && !error && (
        <>
          <AnimatedSpan delay={2000} className='text-green-400'>
            <span>✔ 用户身份验证成功</span>
          </AnimatedSpan>

          <AnimatedSpan delay={2500} className='text-green-400'>
            <span>✔ 权限检查通过</span>
          </AnimatedSpan>
        </>
      )}

      {isCreatingSession && agentName && !error && (
        <>
          <AnimatedSpan delay={3000} className='text-yellow-400'>
            <span>⏳ 正在创建 {agentName} 新会话...</span>
          </AnimatedSpan>

          <AnimatedSpan delay={3500} className='text-cyan-400'>
            <span>ℹ 配置会话参数</span>
          </AnimatedSpan>

          <AnimatedSpan delay={4000} className='text-cyan-400'>
            <span>ℹ 初始化对话上下文</span>
          </AnimatedSpan>
        </>
      )}

      {error && (
        <>
          <AnimatedSpan delay={2000} className='text-red-400'>
            <span>✗ 错误: {error}</span>
          </AnimatedSpan>

          {retryCount > 0 && (
            <AnimatedSpan delay={2500} className='text-yellow-400'>
              <span>⏳ 正在重试... (尝试 {retryCount}/3)</span>
            </AnimatedSpan>
          )}

          {retryCount >= 3 && (
            <AnimatedSpan delay={3000} className='text-red-400'>
              <span>✗ 重试次数已达上限，请刷新页面重试</span>
            </AnimatedSpan>
          )}
        </>
      )}

      {!isLoading && !isCreatingSession && !error && (
        <>
          <AnimatedSpan delay={3000} className='text-green-400'>
            <span>✔ 会话创建成功</span>
          </AnimatedSpan>

          <TypingAnimation delay={3500} className='text-gray-400'>
            正在跳转到对话界面...
          </TypingAnimation>
        </>
      )}
    </LoadingTerminal>
  );
};

interface SessionLoadingTerminalProps {
  sessionTitle: string;
}

export const SessionLoadingTerminal = ({ sessionTitle }: SessionLoadingTerminalProps) => {
  return (
    <LoadingTerminal>
      <TypingAnimation>{`> 加载会话: ${sessionTitle}`}</TypingAnimation>

      <AnimatedSpan delay={1000} className='text-green-400'>
        <span>✔ 连接到会话服务器</span>
      </AnimatedSpan>

      <AnimatedSpan delay={1500} className='text-green-400'>
        <span>✔ 验证会话权限</span>
      </AnimatedSpan>

      <AnimatedSpan delay={2000} className='text-yellow-400'>
        <span>⏳ 正在加载历史消息...</span>
      </AnimatedSpan>

      <AnimatedSpan delay={2500} className='text-cyan-400'>
        <span>ℹ 恢复对话上下文</span>
      </AnimatedSpan>

      <AnimatedSpan delay={3000} className='text-cyan-400'>
        <span>ℹ 初始化组件</span>
      </AnimatedSpan>

      <AnimatedSpan delay={3500} className='text-green-400'>
        <span>✔ 会话加载完成</span>
      </AnimatedSpan>

      <TypingAnimation delay={4000} className='text-gray-400'>
        准备就绪，可以开始对话了！
      </TypingAnimation>
    </LoadingTerminal>
  );
};
